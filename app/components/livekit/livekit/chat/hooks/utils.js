import * as React from 'react';

/**
 * Hook to format chat messages
 * @param {import('@livekit/components-react').ReceivedChatMessage} entry - The chat message entry
 * @param {import('@livekit/components-react').MessageFormatter} [messageFormatter] - Optional message formatter
 * @returns {{message: string, hasBeenEdited: boolean, time: Date, locale: string, name: string}}
 */
export const useChatMessage = (entry, messageFormatter) => {
  const formattedMessage = React.useMemo(() => {
    return messageFormatter ? messageFormatter(entry.message) : entry.message;
  }, [entry.message, messageFormatter]);
  const hasBeenEdited = !!entry.editTimestamp;
  const time = new Date(entry.timestamp);
  const locale = typeof navigator !== 'undefined' ? navigator.language : 'en-US';

  const name = entry.from?.name && entry.from.name !== '' ? entry.from.name : entry.from?.identity;

  return { message: formattedMessage, hasBeenEdited, time, locale, name };
};
